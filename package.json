{"name": "api-manage", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/", "generate:routes": "node scripts/generate-route-name.js"}, "dependencies": {"@zag-js/splitter": "^1.18.2", "@zag-js/vue": "^1.18.2", "ant-design-vue": "4.x", "axios": "^1.10.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-hooks-plus": "^2.4.0", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/eslint-plugin": "^1.1.39", "@vue-hooks-plus/resolvers": "^1.3.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "prettier": "3.5.3", "tsx": "^4.20.3", "typescript": "~5.8.0", "unocss": "^66.2.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.2.4", "vite-plugin-pages": "^0.33.0", "vite-plugin-vue-devtools": "^7.7.2", "vite-plugin-vue-layouts": "^0.11.0", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}, "pnpm": {"onlyBuiltDependencies": ["core-js", "esbuild"]}, "volta": {"node": "22.16.0"}}