// EventBus 类型定义
export type EventListener<T = any> = (data: T) => void | Promise<void>

export interface EventListenerInfo<T = any> {
  listener: EventListener<T>
  once?: boolean
  id: string
}

// 事件映射接口，可以被扩展
export interface DefaultEventMap {
  [key: string]: any
}

/**
 * 类型安全的事件总线类
 */
export class EventBus<EventMap extends DefaultEventMap = DefaultEventMap> {
  private listeners = new Map<keyof EventMap, EventListenerInfo[]>()
  private listenerIdCounter = 0
  private readonly debug: boolean

  constructor(options: { debug?: boolean } = {}) {
    this.debug = options.debug ?? false
  }

  /**
   * 注册事件监听器
   */
  on<K extends keyof EventMap>(event: K, listener: EventListener<EventMap[K]>): () => void {
    const listenerId = `listener_${++this.listenerIdCounter}`
    const listenerInfo: EventListenerInfo<EventMap[K]> = {
      listener,
      id: listenerId,
    }

    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }

    this.listeners.get(event)!.push(listenerInfo)

    if (this.debug) {
      console.log(`[EventBus] 注册监听器: ${String(event)}, ID: ${listenerId}`)
    }

    // 返回移除监听器的函数
    return () => this.off(event, listener)
  }

  /**
   * 注册一次性事件监听器
   */
  once<K extends keyof EventMap>(event: K, listener: EventListener<EventMap[K]>): () => void {
    const listenerId = `listener_${++this.listenerIdCounter}`
    const listenerInfo: EventListenerInfo<EventMap[K]> = {
      listener,
      once: true,
      id: listenerId,
    }

    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }

    this.listeners.get(event)!.push(listenerInfo)

    if (this.debug) {
      console.log(`[EventBus] 注册一次性监听器: ${String(event)}, ID: ${listenerId}`)
    }

    return () => this.off(event, listener)
  }

  /**
   * 触发事件
   */
  async emit<K extends keyof EventMap>(
    event: K,
    ...args: EventMap[K] extends undefined ? [] : [EventMap[K]]
  ): Promise<void> {
    const eventListeners = this.listeners.get(event)
    if (!eventListeners || eventListeners.length === 0) {
      if (this.debug) {
        console.log(`[EventBus] 没有找到事件监听器: ${String(event)}`)
      }
      return
    }

    if (this.debug) {
      console.log(`[EventBus] 触发事件: ${String(event)}, 监听器数量: ${eventListeners.length}`)
    }

    const data = args[0]
    const listenersToRemove: string[] = []

    // 执行所有监听器
    const promises = eventListeners.map(async (listenerInfo) => {
      try {
        await listenerInfo.listener(data)

        // 标记一次性监听器待移除
        if (listenerInfo.once) {
          listenersToRemove.push(listenerInfo.id)
        }
      } catch (error) {
        console.error(`[EventBus] 监听器执行出错: ${String(event)}`, error)
      }
    })

    // 等待所有监听器执行完成
    await Promise.all(promises)

    // 移除一次性监听器
    if (listenersToRemove.length > 0) {
      const remainingListeners = eventListeners.filter(
        (info) => !listenersToRemove.includes(info.id),
      )

      if (remainingListeners.length === 0) {
        this.listeners.delete(event)
      } else {
        this.listeners.set(event, remainingListeners)
      }
    }
  }

  /**
   * 移除特定事件监听器
   */
  off<K extends keyof EventMap>(event: K, listener?: EventListener<EventMap[K]>): void {
    const eventListeners = this.listeners.get(event)
    if (!eventListeners) return

    if (!listener) {
      // 移除该事件的所有监听器
      this.listeners.delete(event)
      if (this.debug) {
        console.log(`[EventBus] 移除所有监听器: ${String(event)}`)
      }
      return
    }

    // 移除特定监听器
    const filteredListeners = eventListeners.filter((info) => info.listener !== listener)

    if (filteredListeners.length === 0) {
      this.listeners.delete(event)
    } else {
      this.listeners.set(event, filteredListeners)
    }

    if (this.debug) {
      console.log(`[EventBus] 移除监听器: ${String(event)}`)
    }
  }

  /**
   * 清空所有监听器或特定事件的监听器
   */
  clear<K extends keyof EventMap>(event?: K): void {
    if (event) {
      this.listeners.delete(event)
      if (this.debug) {
        console.log(`[EventBus] 清空事件监听器: ${String(event)}`)
      }
    } else {
      this.listeners.clear()
      if (this.debug) {
        console.log('[EventBus] 清空所有监听器')
      }
    }
  }

  /**
   * 获取事件监听器数量
   */
  listenerCount<K extends keyof EventMap>(event: K): number {
    const eventListeners = this.listeners.get(event)
    return eventListeners ? eventListeners.length : 0
  }

  /**
   * 获取所有事件名称
   */
  eventNames(): (keyof EventMap)[] {
    return Array.from(this.listeners.keys())
  }

  /**
   * 检查是否有监听器
   */
  hasListeners<K extends keyof EventMap>(event: K): boolean {
    return this.listenerCount(event) > 0
  }
}

// 默认全局实例
export const globalEventBus = new EventBus({ debug: import.meta.env.DEV })
