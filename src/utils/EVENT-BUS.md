# EventBus 使用指南

一个类型安全、功能完整的 Vue 3 事件总线系统。

## 🚀 主要特性

- **类型安全**: 完整的 TypeScript 支持，编译时检查事件类型
- **自动清理**: 组件卸载时自动清理事件监听器，防止内存泄漏
- **异步支持**: 支持异步事件监听器
- **错误处理**: 监听器执行错误不会影响其他监听器
- **调试友好**: 开发环境下提供详细的调试信息
- **多种模式**: 支持一次性监听、响应式状态等高级功能

## 📦 核心文件

```
src/
├── utils/event-bus.ts          # EventBus 核心实现
├── composables/useEventBus.ts  # Vue 3 组合式函数
├── types/events.ts             # 事件类型定义
└── examples/EventBusExample.vue # 使用示例
```

## 🔧 基础使用

### 1. 定义事件类型

```typescript
// src/types/events.ts
export interface AppEventMap {
  'user:login': { userId: string; username: string }
  'user:logout': void
  'notification:show': { type: 'success' | 'error'; message: string }
}
```

### 2. 在组件中使用

```vue
<script setup lang="ts">
import { useEventBus } from '@/composables/useEventBus'
import type { AppEventMap } from '@/types/events'

const { on, emit, once } = useEventBus<AppEventMap>()

// 监听事件
on('user:login', (data) => {
  console.log('用户登录:', data.username)
})

// 触发事件
const handleLogin = () => {
  emit('user:login', {
    userId: '123',
    username: 'john',
  })
}

// 一次性监听
once('user:logout', () => {
  console.log('用户登出（只监听一次）')
})
</script>
```

## 🎯 高级功能

### 响应式事件状态

```typescript
const [theme, setTheme] = useEventState('theme:change', { theme: 'light' })

// 主题会随事件自动更新
setTheme({ theme: 'dark' })
```

### 组件间通信

```typescript
const { send, listen } = useEventCommunication('modal')

// 发送数据
send('open', { modalId: 'user-settings' })

// 监听数据
listen('close', (data) => {
  console.log('模态框关闭:', data)
})
```

### 直接使用 EventBus 类

```typescript
import { EventBus } from '@/utils/event-bus'

const bus = new EventBus({ debug: true })

// 注册监听器（返回清理函数）
const cleanup = bus.on('test', (data) => {
  console.log('接收到:', data)
})

// 触发事件
await bus.emit('test', 'hello world')

// 手动清理
cleanup()
```

## 📋 API 参考

### EventBus 类方法

- `on<K>(event, listener)` - 注册事件监听器
- `once<K>(event, listener)` - 注册一次性监听器
- `emit<K>(event, data)` - 触发事件
- `off<K>(event, listener?)` - 移除监听器
- `clear(event?)` - 清空监听器
- `listenerCount(event)` - 获取监听器数量
- `hasListeners(event)` - 检查是否有监听器

### useEventBus 组合函数

```typescript
const {
  on, // 注册监听器（自动清理）
  once, // 一次性监听器（自动清理）
  emit, // 触发事件
  off, // 移除监听器
  listenerCount, // 监听器数量
  hasListeners, // 检查监听器
} = useEventBus<EventMap>()
```

### useEventState 响应式状态

```typescript
const [state, setState] = useEventState<T>(
  eventName: string,
  initialValue: T,
  eventBus?: EventBus
)
```

### useEventCommunication 组件通信

```typescript
const {
  send,      // 发送事件
  listen,    // 监听事件
  listenOnce // 一次性监听
} = useEventCommunication<T>(eventPrefix: string)
```

## ⚡ 最佳实践

### 1. 事件命名规范

```typescript
// 推荐：使用命名空间
'user:login' // 用户登录
'modal:open' // 模态框打开
'data:refresh' // 数据刷新

// 避免：模糊的事件名
'login' // 太泛化
'update' // 不明确
```

### 2. 类型安全

```typescript
// 始终定义事件类型
interface MyEventMap {
  'custom:event': { id: number; name: string }
}

// 使用类型化的 EventBus
const { emit } = useEventBus<MyEventMap>()
emit('custom:event', { id: 1, name: 'test' }) // ✅ 类型安全
```

### 3. 自动清理

```vue
<script setup>
// ✅ 推荐：使用 useEventBus（自动清理）
const { on } = useEventBus()
on('event', handler)

// ❌ 避免：直接使用全局实例（需要手动清理）
globalEventBus.on('event', handler)
</script>
```

### 4. 错误处理

```typescript
// 监听器中的错误不会影响其他监听器
on('user:login', async (data) => {
  try {
    await processLogin(data)
  } catch (error) {
    console.error('登录处理失败:', error)
    // 错误已被捕获，不会影响其他监听器
  }
})
```

## 🐛 调试技巧

### 开启调试模式

```typescript
// 开发环境自动开启调试
const bus = new EventBus({ debug: true })

// 或者使用环境变量
const bus = new EventBus({ debug: import.meta.env.DEV })
```

### 监控事件状态

```typescript
const { hasListeners, listenerCount } = useEventBus()

console.log('是否有监听器:', hasListeners('user:login'))
console.log('监听器数量:', listenerCount('user:login'))
```

## 🚨 注意事项

1. **内存泄漏防护**: 使用 `useEventBus` 自动清理，或手动调用返回的清理函数
2. **异步监听器**: 支持 async/await，但要注意错误处理
3. **事件命名**: 使用命名空间避免事件名冲突
4. **类型定义**: 及时更新 `AppEventMap` 接口
5. **调试信息**: 生产环境关闭 debug 模式以提高性能

## 📝 迁移指南

从传统 EventBus 迁移：

```typescript
// 旧方式
this.$eventBus.$on('event', handler)
this.$eventBus.$emit('event', data)

// 新方式
const { on, emit } = useEventBus()
on('event', handler) // 自动清理
emit('event', data)
```
