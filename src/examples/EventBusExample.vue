<template>
  <div class="event-bus-example">
    <h2>EventBus 使用示例</h2>

    <!-- 基础使用示例 -->
    <div class="example-section">
      <h3>1. 基础事件监听与触发</h3>
      <div class="controls">
        <button @click="sendNotification('success')">发送成功通知</button>
        <button @click="sendNotification('error')">发送错误通知</button>
        <button @click="clearNotifications">清空通知</button>
      </div>
      <div class="output">
        <p>监听到的通知: {{ notifications.length }} 条</p>
        <div v-for="(notif, index) in notifications" :key="index" class="notification">
          <span :class="notif.type">{{ notif.type.toUpperCase() }}: {{ notif.message }}</span>
        </div>
      </div>
    </div>

    <!-- 一次性监听示例 -->
    <div class="example-section">
      <h3>2. 一次性事件监听</h3>
      <div class="controls">
        <button @click="triggerOnceEvent">触发一次性事件</button>
        <button @click="resetOnceListener">重置一次性监听器</button>
      </div>
      <div class="output">
        <p>一次性事件触发次数: {{ onceEventCount }}</p>
        <p>监听器状态: {{ onceListenerActive ? '活跃' : '已销毁' }}</p>
      </div>
    </div>

    <!-- 响应式状态示例 -->
    <div class="example-section">
      <h3>3. 响应式事件状态</h3>
      <div class="controls">
        <button @click="switchTheme('light')">浅色主题</button>
        <button @click="switchTheme('dark')">深色主题</button>
        <button @click="switchTheme('auto')">自动主题</button>
      </div>
      <div class="output">
        <p>当前主题: {{ currentTheme }}</p>
      </div>
    </div>

    <!-- 组件通信示例 -->
    <div class="example-section">
      <h3>4. 组件间通信</h3>
      <div class="controls">
        <button @click="openModal">打开模态框</button>
        <button @click="sendToChild">发送数据给子组件</button>
      </div>
      <div class="output">
        <p>模态框状态: {{ modalStatus }}</p>
        <p>子组件接收到的数据: {{ childData }}</p>
      </div>
    </div>

    <!-- 事件统计 -->
    <div class="example-section">
      <h3>5. 事件统计信息</h3>
      <div class="output">
        <p>当前活跃事件类型: {{ activeEvents.join(', ') || '无' }}</p>
        <p>通知事件监听器数量: {{ notificationListenerCount }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useEventBus, useEventState, useEventCommunication } from '@/composables/useEventBus'
import type { AppEventMap } from '@/types/events'

// 使用类型安全的 EventBus
const { on, once, emit, listenerCount, hasListeners } = useEventBus<AppEventMap>()

// 基础使用示例数据
const notifications = ref<Array<{ type: string; message: string }>>([])

// 一次性监听示例数据
const onceEventCount = ref(0)
const onceListenerActive = ref(false)

// 响应式状态示例
const [currentTheme, setTheme] = useEventState<{ theme: 'light' | 'dark' | 'auto' }>(
  'theme:change',
  { theme: 'light' },
)

// 组件通信示例
const { send, listen } = useEventCommunication<any>('example')
const modalStatus = ref('关闭')
const childData = ref('')

// 事件统计
const activeEvents = ref<string[]>([])
const notificationListenerCount = ref(0)

// 基础事件处理
const sendNotification = (type: 'success' | 'error') => {
  emit('notification:show', {
    type,
    message: `这是一个${type === 'success' ? '成功' : '错误'}通知 - ${Date.now()}`,
  })
}

const clearNotifications = () => {
  emit('notification:clear', {})
  notifications.value = []
}

// 一次性事件处理
const triggerOnceEvent = () => {
  emit('user:login', {
    userId: 'user123',
    username: 'testuser',
  })
}

const resetOnceListener = () => {
  setupOnceListener()
  onceListenerActive.value = true
}

// 主题切换
const switchTheme = (theme: 'light' | 'dark' | 'auto') => {
  setTheme({ theme })
}

// 组件通信
const openModal = () => {
  emit('modal:open', {
    modalId: 'example-modal',
    data: { title: '示例模态框' },
  })
}

const sendToChild = () => {
  send('data-update', {
    timestamp: Date.now(),
    message: '来自父组件的数据',
  })
}

// 设置一次性监听器
const setupOnceListener = () => {
  once('user:login', (data) => {
    onceEventCount.value++
    onceListenerActive.value = false
    console.log('用户登录事件（一次性）:', data)
  })
}

// 更新统计信息
const updateStats = () => {
  const events = ['notification:show', 'user:login', 'theme:change', 'modal:open']
  activeEvents.value = events.filter((event) => hasListeners(event as keyof AppEventMap))
  notificationListenerCount.value = listenerCount('notification:show')
}

onMounted(() => {
  // 监听通知事件
  on('notification:show', (data) => {
    notifications.value.push({
      type: data.type,
      message: data.message,
    })
    updateStats()
  })

  on('notification:clear', () => {
    notifications.value = []
  })

  // 监听模态框事件
  on('modal:open', (data) => {
    modalStatus.value = `打开: ${data.modalId}`
    setTimeout(() => {
      emit('modal:close', { modalId: data.modalId, result: 'success' })
    }, 2000)
  })

  on('modal:close', (data) => {
    modalStatus.value = `关闭: ${data.modalId}`
  })

  // 监听子组件通信
  listen('data-update', (data) => {
    childData.value = JSON.stringify(data)
  })

  // 设置一次性监听器
  setupOnceListener()
  onceListenerActive.value = true

  // 定期更新统计信息
  const interval = setInterval(updateStats, 1000)

  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<script lang="ts">
import { onUnmounted } from 'vue'
</script>

<style scoped>
.event-bus-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.example-section h3 {
  margin-top: 0;
  color: #2c3e50;
}

.controls {
  margin-bottom: 15px;
}

.controls button {
  margin-right: 10px;
  margin-bottom: 5px;
  padding: 8px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.controls button:hover {
  background: #2980b9;
}

.output {
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.notification {
  padding: 5px 0;
}

.success {
  color: #27ae60;
  font-weight: bold;
}

.error {
  color: #e74c3c;
  font-weight: bold;
}
</style>
