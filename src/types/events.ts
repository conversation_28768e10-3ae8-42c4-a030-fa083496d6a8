export const EVENT_NAMES = {
  USER_LOGIN: 'user:login',
  USER_LOGOUT: 'user:logout',
  USER_PROFILE_UPDATE: 'user:profile-update',
  THEME_CHANGE: 'theme:change',
  NOTIFICATION_SHOW: 'notification:show',
  NOTIFICATION_CLEAR: 'notification:clear',
  ROUTE_BEFORE_CHANGE: 'route:before-change',
  ROUTE_AFTER_CHANGE: 'route:after-change',
  DATA_REFRESH: 'data:refresh',
  DATA_CACHE_CLEAR: 'data:cache-clear',
  MODAL_OPEN: 'modal:open',
  MODAL_CLOSE: 'modal:close',
  DIALOG_CONFIRM: 'dialog:confirm',
  SYSTEM_ONLINE: 'system:online',
  SYSTEM_OFFLINE: 'system:offline',
  SYSTEM_ERROR: 'system:error',
} as const

/**
 * 全局事件类型定义
 * 在这里定义所有应用中使用的事件类型
 */
export interface AppEventMap {
  // 用户相关事件
  [EVENT_NAMES.USER_LOGIN]: { userId: string; username: string; avatar?: string }
  [EVENT_NAMES.USER_LOGOUT]: void
  [EVENT_NAMES.USER_PROFILE_UPDATE]: { userId: string; changes: Record<string, any> }

  // 主题相关事件
  [EVENT_NAMES.THEME_CHANGE]: { theme: 'light' | 'dark' | 'auto' }

  // 通知相关事件
  [EVENT_NAMES.NOTIFICATION_SHOW]: {
    type: 'success' | 'error' | 'warning' | 'info'
    message: string
    duration?: number
  }
  [EVENT_NAMES.NOTIFICATION_CLEAR]: { id?: string }

  // 路由相关事件
  [EVENT_NAMES.ROUTE_BEFORE_CHANGE]: { from: string; to: string }
  [EVENT_NAMES.ROUTE_AFTER_CHANGE]: { route: string }

  // 数据更新事件
  [EVENT_NAMES.DATA_REFRESH]: { module: string }
  [EVENT_NAMES.DATA_CACHE_CLEAR]: { key?: string }

  // 组件通信事件
  [EVENT_NAMES.MODAL_OPEN]: { modalId: string; data?: any }
  [EVENT_NAMES.MODAL_CLOSE]: { modalId: string; result?: any }
  [EVENT_NAMES.DIALOG_CONFIRM]: { dialogId: string; confirmed: boolean }

  // 系统事件
  [EVENT_NAMES.SYSTEM_ONLINE]: void
  [EVENT_NAMES.SYSTEM_OFFLINE]: void
  [EVENT_NAMES.SYSTEM_ERROR]: { error: Error; context?: string }
}
