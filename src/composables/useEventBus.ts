import { onUnmounted, ref } from 'vue'
import {
  EventBus,
  globalEventBus,
  type DefaultEventMap,
  type EventListener,
} from '@/utils/event-bus'

/**
 * Vue 3 EventBus 组合式函数
 * 提供自动清理功能，防止内存泄漏
 */
export function useEventBus<EventMap extends DefaultEventMap = DefaultEventMap>(
  eventBus?: EventBus<EventMap>,
) {
  const bus = eventBus || (globalEventBus as unknown as EventBus<EventMap>)
  // 存储当前组件注册的监听器清理函数
  const cleanupFunctions: (() => void)[] = []

  /**
   * 注册事件监听器（组件卸载时自动清理）
   */
  const on = <K extends keyof EventMap>(event: K, listener: EventListener<EventMap[K]>) => {
    const cleanup = bus.on(event, listener)
    cleanupFunctions.push(cleanup)
    return cleanup
  }

  /**
   * 注册一次性事件监听器（组件卸载时自动清理）
   */
  const once = <K extends keyof EventMap>(event: K, listener: EventListener<EventMap[K]>) => {
    const cleanup = bus.once(event, listener)
    cleanupFunctions.push(cleanup)
    return cleanup
  }

  /**
   * 触发事件
   */
  const emit = <K extends keyof EventMap>(
    event: K,
    ...args: EventMap[K] extends undefined ? [] : [EventMap[K]]
  ) => {
    return bus.emit(event, ...args)
  }

  /**
   * 移除事件监听器
   */
  const off = <K extends keyof EventMap>(event: K, listener?: EventListener<EventMap[K]>) => {
    bus.off(event, listener)
  }

  /**
   * 获取监听器数量
   */
  const listenerCount = <K extends keyof EventMap>(event: K) => {
    return bus.listenerCount(event)
  }

  /**
   * 检查是否有监听器
   */
  const hasListeners = <K extends keyof EventMap>(event: K) => {
    return bus.hasListeners(event)
  }

  // 组件卸载时自动清理所有监听器
  onUnmounted(() => {
    cleanupFunctions.forEach((cleanup) => cleanup())
    cleanupFunctions.length = 0
  })

  return {
    on,
    once,
    emit,
    off,
    listenerCount,
    hasListeners,
  }
}

/**
 * 创建响应式事件状态
 * 监听事件并将最新值存储在 ref 中
 */
export function useEventState<T>(
  eventName: string,
  initialValue: T,
  eventBus: EventBus = globalEventBus,
) {
  const state = ref(initialValue)

  const { on, emit } = useEventBus(eventBus)

  // 监听事件更新状态
  on(eventName, (newValue: T) => {
    state.value = newValue
  })

  // 发送状态更新事件
  const setState = (value: T) => {
    return emit(eventName, value)
  }

  return [state, setState] as const
}

/**
 * 创建事件通信 Hook
 * 用于父子组件或兄弟组件间通信
 */
export function useEventCommunication<T = any>(eventPrefix: string) {
  const { on, emit } = useEventBus()

  const send = (action: string, data?: T) => {
    return emit(`${eventPrefix}:${action}`, data)
  }

  const listen = (action: string, listener: EventListener<T>) => {
    return on(`${eventPrefix}:${action}`, listener)
  }

  const listenOnce = (action: string, listener: EventListener<T>) => {
    const { once } = useEventBus()
    return once(`${eventPrefix}:${action}`, listener)
  }

  return {
    send,
    listen,
    listenOnce,
  }
}
