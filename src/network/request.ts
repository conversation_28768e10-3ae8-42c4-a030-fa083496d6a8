import axios, { type AxiosInstance } from 'axios'
import { setupRequestInterceptor } from './interceptors/request-interceptors'
import { setupResponseInterceptor } from './interceptors/response-interceptors'

// 创建 axios 实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 设置拦截器
setupRequestInterceptor(request)
setupResponseInterceptor(request)

export default request
