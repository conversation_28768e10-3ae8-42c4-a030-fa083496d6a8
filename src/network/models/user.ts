import { BaseModel } from '..'

export interface BackendUser {
  id: string
  user_name: string
  user_email: string
  user_phone: string
}

export class User extends BaseModel {
  id: string
  userName: string
  userEmail: string
  userPhone: string

  constructor(data: BackendUser) {
    super()
    this.id = data.id
    this.userName = data.user_name
    this.userEmail = data.user_email
    this.userPhone = data.user_phone
  }

  toBackend(): BackendUser {
    return {
      id: this.id,
      user_name: this.userName,
      user_email: this.userEmail,
      user_phone: this.userPhone,
    }
  }
}
