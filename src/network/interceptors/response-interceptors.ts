import type { AxiosInstance, AxiosResponse } from 'axios'
import { message } from 'ant-design-vue'
import { useAuthStore } from '@/stores/auth'

export function setupResponseInterceptor(request: AxiosInstance) {
  request.interceptors.response.use(
    (response: AxiosResponse) => {
      const { data } = response

      // 统一处理业务错误
      if (data.code !== 200) {
        message.error(data.message || '请求失败')
        return Promise.reject(new Error(data.message))
      }

      return response
    },
    (error) => {
      // 处理 HTTP 错误
      if (error.response?.status === 401) {
        const authStore = useAuthStore()
        authStore.logout()
        message.error('登录已过期，请重新登录')
      } else {
        message.error(error.message || '网络错误')
      }

      return Promise.reject(error)
    },
  )
}
