import type { AxiosInstance, InternalAxiosRequestConfig, AxiosError } from 'axios'
import { useAuthStore } from '@/stores/auth'

export function setupRequestInterceptor(request: AxiosInstance) {
  request.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      // 添加认证 token
      const authStore = useAuthStore()
      if (authStore.token) {
        config.headers.Authorization = `Bearer ${authStore.token}`
      }

      // 添加请求时间戳
      config.metadata = { startTime: Date.now() }

      return config
    },
    (error: AxiosError) => {
      return Promise.reject(error)
    },
  )
}
