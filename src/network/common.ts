// 通用响应结构
export class ApiResponse<T, IsList = false> {
  code: number
  message: string
  data: IsList extends true ? PageResponse<T> : T

  constructor(obj: Record<string, any>) {
    this.code = obj.code
    this.message = obj.message
    this.data = obj.data
  }
}

// 分页参数
export interface PageParams {
  pageIndex: number
  pageSize: number
}

// 分页响应
export class PageResponse<T> {
  list: T[]
  total: number
  pageIndex: number
  pageSize: number

  constructor(obj: Record<string, any>) {
    this.list = obj.list
    this.total = obj.total
    this.pageIndex = obj.pageIndex
    this.pageSize = obj.pageSize
  }
}

export abstract class BaseModel {
  abstract toBackend(): Record<string, any>
}
