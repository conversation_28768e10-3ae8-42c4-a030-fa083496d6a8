<script setup lang="ts">
import * as splitter from '@zag-js/splitter'
import { normalizeProps, useMachine } from '@zag-js/vue'
import { computed, provide, useSlots } from 'vue'

const props = defineProps<{
  orientation?: 'horizontal' | 'vertical'
  defaultSize?: number[]
}>()
const slots = useSlots()

const panelSlots = computed(() => {
  const children = slots.default?.()
  if (!children?.length) return []
  return children.filter((slot) => (slot.type as any).__name === 'SplitterPanel')
})
const panels = computed(() => {
  if (!panelSlots.value.length) return []
  return panelSlots.value.map((item) => ({
    id: item.props?.id,
    minSize: item.props?.minSize || 10,
  }))
})
const sizeList = computed(() => {
  if (props.defaultSize) return props.defaultSize
  const count = panelSlots.value.length
  if (!count) return []
  const results = Array(count).fill(-1)
  let total = 0,
    notSetCount = count
  for (let i = 0; i < count; i++) {
    const element = panelSlots.value[i]
    if (element.props?.size) {
      notSetCount--
      total += element.props.size
      results[i] = element.props.size
    }
  }
  let remain = 100 - total
  const avg = remain / notSetCount
  for (let i = 0; i < count; i++) {
    if (results[i] === -1) results[i] = avg
  }
  return results
})
const userProps = computed(() => ({
  id: 'splitter',
  defaultSize: sizeList.value,
  orientation: props.orientation,
  panels: panels.value,
}))

const service = useMachine(splitter.machine, userProps)
const api = computed(() => splitter.connect(service, normalizeProps))
</script>

<template>
  <div v-bind="api.getRootProps()">
    <template v-for="(panel, index) in panelSlots" :key="index">
      <component :is="panel" v-bind="api.getPanelProps({ id: panel.props!.id })" />
      <div
        v-if="index < panelSlots.length - 1"
        v-bind="
          api.getResizeTriggerProps({ id: `${panel.props!.id}:${panelSlots[index + 1].props!.id}` })
        "
      ></div>
    </template>
  </div>
</template>

<style scoped>
/* Base styles for the resize trigger */
[data-scope='splitter'][data-part='resize-trigger'] {
  background: #f0f2f53b; /* A more subtle, modern background color */
  position: relative;
  transition: background-color 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Grip indicator using a pseudo-element */
[data-scope='splitter'][data-part='resize-trigger']::after {
  content: '';
  display: block;
  background-color: #c0c4cc; /* Color of the grip dots */
  transition: background-color 0.2s ease-in-out;
}

/* Horizontal splitter (vertical bar) */
[data-scope='splitter'][data-part='resize-trigger'][data-orientation='horizontal'] {
  width: 12px; /* Slightly wider for easier grabbing */
  cursor: col-resize;
  border-left: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
}

[data-scope='splitter'][data-part='resize-trigger'][data-orientation='horizontal']::after {
  width: 2px;
  height: 32px; /* Vertical line as a grip */
  border-radius: 2px;
}

/* Vertical splitter (horizontal bar) */
[data-scope='splitter'][data-part='resize-trigger'][data-orientation='vertical'] {
  height: 12px; /* Slightly thicker */
  cursor: row-resize;
  margin: -5px 0;
  border-top: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  width: 100%; /* Changed from 100vw to be contained within its parent */
}

[data-scope='splitter'][data-part='resize-trigger'][data-orientation='vertical']::after {
  height: 2px;
  width: 32px; /* Horizontal line as a grip */
  border-radius: 2px;
}

/* Hover/Focus state */
[data-scope='splitter'][data-part='resize-trigger'][data-focus],
[data-scope='splitter'][data-part='resize-trigger']:hover {
  background: #e4e7eda7; /* A slightly darker background on hover */
}

[data-scope='splitter'][data-part='resize-trigger'][data-focus]::after,
[data-scope='splitter'][data-part='resize-trigger']:hover::after {
  background-color: #888a8d8b; /* Darker grip on hover */
}

/* Active (dragging) state */
[data-scope='splitter'][data-part='resize-trigger']:active {
  background: #dcdfe6; /* Even more prominent background */
}
[data-scope='splitter'][data-part='resize-trigger']:active::after {
  background-color: #707071; /* Use a primary color for the grip when active */
}

/* Disabled state */
[data-scope='splitter'][data-part='resize-trigger'][data-disabled] {
  background: #f5f7fa;
  cursor: not-allowed;
}

[data-scope='splitter'][data-part='resize-trigger'][data-disabled]::after {
  background-color: #e4e7ed;
}
</style>
