<route lang="json">
{
  "name": "setting",
  "meta": {
    "title": "系统设置"
  }
}
</route>

<script setup lang="ts">
import routeNames from '@/generated/route-names'

defineOptions({
  name: 'SettingPage',
})

const router = useRouter()

function handleClick() {
  router.push({ name: routeNames.home })
}
</script>

<template>
  <div>
    <a-button @click="handleClick">跳转到首页</a-button>
  </div>
</template>

<style scoped></style>
