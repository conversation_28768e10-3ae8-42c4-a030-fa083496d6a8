<route lang="yaml">
meta:
  layout: false
</route>
<script setup lang="ts">
import Splitter from '@/components/splitter'
import CanvasDemo from './components/CanvasDemo.vue'

const panels = [
  { id: 'a', minSize: 10 },
  { id: 'b', minSize: 10 },
  { id: 'c', minSize: 10 },
  { id: 'd', minSize: 10 },
]
</script>

<template>
  <div class="h-100vh w-100vw">
    <Splitter orientation="horizontal" :default-size="[20, 60, 20]">
      <Splitter.Panel id="a">
        <p>A</p>
      </Splitter.Panel>
      <Splitter.Panel id="b">
        <CanvasDemo />
      </Splitter.Panel>
      <Splitter.Panel id="c">
        <p>C</p>
      </Splitter.Panel>
    </Splitter>
  </div>
</template>

<style scoped></style>
