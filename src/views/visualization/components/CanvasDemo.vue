<template>
  <div
    ref="canvasContainerRef"
    class="canvas-container"
    @wheel="handleWheel"
    @mousedown="handleMouseDown"
  >
    <div class="info-panel">
      <p>按住 <b>Space</b> + <b>鼠标左键</b> 拖拽</p>
      <p>使用 <b>鼠标滚轮</b> 缩放</p>
      <p>缩放: {{ (scale * 100).toFixed(0) }}%</p>
    </div>
    <div ref="artboardRef" class="artboard" :style="artboardStyle">
      <div class="artboard-content">点击或拖拽左侧的图表到此处</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// --- 状态定义 ---
const scale = ref(1)
const translateX = ref(0)
const translateY = ref(0)

const isSpacePressed = ref(false)
const isDragging = ref(false)
const lastMouseX = ref(0)
const lastMouseY = ref(0)

const canvasContainerRef = ref<HTMLElement | null>(null)
const artboardRef = ref<HTMLElement | null>(null)

const MIN_SCALE = 0.2
const MAX_SCALE = 4

// --- 计算属性 ---
const artboardStyle = computed(() => ({
  transform: `translate(${translateX.value}px, ${translateY.value}px) scale(${scale.value})`,
}))

// --- 事件处理 ---

// 1. 滚轮缩放
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  if (!canvasContainerRef.value) return

  const prevScale = scale.value
  const scaleAmount = -event.deltaY / 500

  scale.value = Math.max(MIN_SCALE, Math.min(MAX_SCALE, prevScale + scaleAmount))

  // 以鼠标为中心进行缩放
  const rect = canvasContainerRef.value.getBoundingClientRect()
  const mouseX = event.clientX - rect.left // 鼠标在容器内的 X 坐标
  const mouseY = event.clientY - rect.top // 鼠标在容器内的 Y 坐标

  // V = T + S * v  (V: 视图坐标, T: 平移, S: 缩放, v: 原始坐标)
  // v = (V - T) / S
  // 我们希望缩放后，鼠标指针下的 v 点坐标不变
  // T' = V - S' * v = V - S' * (V - T) / S
  translateX.value = mouseX - (mouseX - translateX.value) * (scale.value / prevScale)
  translateY.value = mouseY - (mouseY - translateY.value) * (scale.value / prevScale)
}

// 2. 拖拽平移
const handleMouseDown = (event: MouseEvent) => {
  if (isSpacePressed.value) {
    event.preventDefault()
    isDragging.value = true
    lastMouseX.value = event.clientX
    lastMouseY.value = event.clientY
    if (canvasContainerRef.value) {
      canvasContainerRef.value.style.cursor = 'grabbing'
    }
  }
}

const handleMouseMove = (event: MouseEvent) => {
  if (isDragging.value) {
    const deltaX = event.clientX - lastMouseX.value
    const deltaY = event.clientY - lastMouseY.value

    translateX.value += deltaX
    translateY.value += deltaY

    lastMouseX.value = event.clientX
    lastMouseY.value = event.clientY
  }
}

const handleMouseUp = () => {
  isDragging.value = false
  if (canvasContainerRef.value) {
    canvasContainerRef.value.style.cursor = isSpacePressed.value ? 'grab' : 'default'
  }
}

// 3. 键盘事件 (Space)
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.code === 'Space') {
    event.preventDefault()
    isSpacePressed.value = true
    if (canvasContainerRef.value && !isDragging.value) {
      canvasContainerRef.value.style.cursor = 'grab'
    }
  }
}

const handleKeyUp = (event: KeyboardEvent) => {
  if (event.code === 'Space') {
    isSpacePressed.value = false
    if (canvasContainerRef.value && !isDragging.value) {
      canvasContainerRef.value.style.cursor = 'default'
    }
  }
}

// --- 生命周期钩子 ---
onMounted(() => {
  // 将画板初始位置居中
  if (canvasContainerRef.value && artboardRef.value) {
    const containerRect = canvasContainerRef.value.getBoundingClientRect()
    const artboardRect = artboardRef.value.getBoundingClientRect()
    translateX.value = (containerRect.width - artboardRect.width) / 2
    translateY.value = (containerRect.height - artboardRect.height) / 2
  }

  window.addEventListener('keydown', handleKeyDown)
  window.addEventListener('keyup', handleKeyUp)
  window.addEventListener('mousemove', handleMouseMove)
  window.addEventListener('mouseup', handleMouseUp)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown)
  window.removeEventListener('keyup', handleKeyUp)
  window.removeEventListener('mousemove', handleMouseMove)
  window.removeEventListener('mouseup', handleMouseUp)
})
</script>

<style scoped>
.canvas-container {
  width: 100%;
  height: 100vh;
  background-color: #f0f2f5;
  position: relative;
  overflow: hidden;
  cursor: default;
  user-select: none;
}

.artboard {
  position: absolute;
  top: 0;
  left: 0;
  width: 90%;
  aspect-ratio: 16 / 9;
  background-color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform-origin: 0 0; /* 将变换的原点设置在左上角 */
  will-change: transform; /* 性能优化 */
}

.artboard-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 24px;
  color: #cccccc;
  box-sizing: border-box;
}

.info-panel {
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 14px;
}

.info-panel p {
  margin: 4px 0;
}
</style>
