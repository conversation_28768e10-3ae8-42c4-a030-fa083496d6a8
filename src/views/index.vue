<route lang="yaml">
meta:
  layout: false
</route>

<script setup lang="ts">
import routeNames from '@/generated/route-names'
import { Descriptions } from 'ant-design-vue'

const route = useRoute()
const router = useRouter()
const path = ref(route.path)
const name = ref(route.name as string)

defineOptions({
  name: 'HomePage',
})

function handleClick() {
  router.push({ name: routeNames.profile })
}

onMounted(() => {
  console.log('路由配置', router.getRoutes())
})
</script>

<template>
  <div>
    <h1>Home Page</h1>
    <Descriptions title="路由配置">
      <Descriptions.Item label="路径">
        {{ path }}
      </Descriptions.Item>
      <Descriptions.Item label="名称">
        {{ name }}
      </Descriptions.Item>
    </Descriptions>
    <a-button type="primary" @click="handleClick">去个人中心</a-button>
  </div>
</template>

<style scoped></style>
