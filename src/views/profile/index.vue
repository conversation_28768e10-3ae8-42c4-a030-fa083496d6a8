<route lang="json">
{
  "name": "profile",
  "meta": {
    "title": "个人中心"
  }
}
</route>

<script setup lang="ts">
import routeNames from '@/generated/route-names'

defineOptions({
  name: 'ProfilePage',
})
const router = useRouter()

function handleClick() {
  router.push({ name: routeNames.setting })
}
</script>

<template>
  <div>
    <a-button @click="handleClick">跳转到设置</a-button>
  </div>
</template>

<style scoped></style>
