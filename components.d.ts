/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AButton: typeof import('ant-design-vue/es')['Button']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutFooter: typeof import('ant-design-vue/es')['LayoutFooter']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Splitter: typeof import('./src/components/splitter/Splitter.vue')['default']
    SplitterPanel: typeof import('./src/components/splitter/SplitterPanel.vue')['default']
    SplitterTrigger: typeof import('./src/components/splitter/SplitterTrigger.vue')['default']
  }
}
