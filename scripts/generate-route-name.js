import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * 查找项目根目录（包含 package.json 的目录）
 */
function findProjectRoot(startDir = __dirname) {
  let currentDir = startDir

  while (currentDir !== path.dirname(currentDir)) {
    const packageJsonPath = path.join(currentDir, 'package.json')
    if (fs.existsSync(packageJsonPath)) {
      return currentDir
    }
    currentDir = path.dirname(currentDir)
  }

  // 如果找不到 package.json，使用当前工作目录
  return process.cwd()
}

/**
 * 从 Vue 文件中提取 <route> 标签中的 meta.title
 */
function extractRouteTitle(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')

    // 匹配 <route> 标签及其内容
    const routeTagMatch = content.match(/<route[^>]*>([\s\S]*?)<\/route>/i)

    if (!routeTagMatch) {
      return null
    }

    const routeContent = routeTagMatch[1].trim()

    try {
      // 解析 JSON 内容
      const routeConfig = JSON.parse(routeContent)
      return routeConfig?.meta?.title || null
    } catch (jsonError) {
      console.log(`⚠️  无法解析 ${filePath} 中的 route 配置: ${jsonError.message}`)
      return null
    }
  } catch (error) {
    console.log(`⚠️  读取文件 ${filePath} 失败: ${error.message}`)
    return null
  }
}

/**
 * 扫描 src/views 目录，生成路由名称对象
 */
function generateRouteNames() {
  const projectRoot = findProjectRoot()
  const viewsDir = path.join(projectRoot, 'src/views')
  const outputDir = path.join(projectRoot, 'src/generated')
  const outputFile = path.join(outputDir, 'route-names.ts')

  console.log('📁 项目根目录:', projectRoot)
  console.log('🔍 扫描视图目录:', viewsDir)

  // 确保输出目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  // 扫描 views 目录
  const routeNames = []

  // 需要排除的目录列表
  const excludedDirs = ['components', '__tests__', 'assets', 'utils']

  try {
    const entries = fs.readdirSync(viewsDir, { withFileTypes: true })

    for (const entry of entries) {
      if (entry.isDirectory()) {
        // 排除指定目录
        if (excludedDirs.includes(entry.name)) {
          console.log(`⚠️  跳过目录 ${entry.name} (排除目录)`)
          continue
        }

        const dirPath = path.join(viewsDir, entry.name)
        const indexVuePath = path.join(dirPath, 'index.vue')

        // 检查是否存在 index.vue 文件
        if (fs.existsSync(indexVuePath)) {
          // 提取路由标题
          const title = extractRouteTitle(indexVuePath)

          routeNames.push({
            name: entry.name,
            title: title,
          })

          console.log(`✅ 发现路由: ${entry.name}${title ? ` (${title})` : ''}`)
        } else {
          console.log(`⚠️  跳过目录 ${entry.name} (缺少 index.vue)`)
        }
      }
    }

    // 生成 TypeScript 文件内容
    const tsContent = `/**
 * 自动生成的路由名称对象
 * 请不要手动修改此文件
 * 运行 node generate-route-name.js 重新生成此文件
 * 生成时间: ${new Date().toLocaleString('zh-CN')}
 */

// 路由名称类型
export type RouteNameType = ${routeNames.map((route) => `'${route.name}'`).join(' | ')}

// 路由名称对象
export const routeNames = {
  /** 首页 */
  home: 'index' as const,
${routeNames
  .map((route) => {
    const comment = route.title ? `  /** ${route.title} */` : ''
    return comment
      ? `${comment}\n  ${route.name}: '${route.name}' as const,`
      : `  ${route.name}: '${route.name}' as const,`
  })
  .join('\n')}
} as const

// 默认导出
export default routeNames

// 类型导出
export type RouteNames = typeof routeNames
`

    // 写入文件
    fs.writeFileSync(outputFile, tsContent, 'utf8')

    console.log('📝 生成路由名称文件:', outputFile)
    console.log('🎉 生成完成! 共发现', routeNames.length, '个路由:')
    routeNames.forEach((route) => {
      console.log(`   - ${route.name}${route.title ? ` (${route.title})` : ''}`)
    })

    // 生成使用示例
    console.log('\n📖 使用示例:')
    console.log('import { routeNames } from "@/generated/route-names"')
    console.log('import { useRouter } from "vue-router"')
    console.log('')
    console.log('const router = useRouter()')
    console.log('// 编程式导航')
    if (routeNames.length > 0) {
      console.log(`router.push({ name: routeNames.${routeNames[0].name} })`)
    }
  } catch (error) {
    console.error('❌ 生成路由名称时出错:', error.message)
    process.exit(1)
  }
}

// 运行生成函数
generateRouteNames()
