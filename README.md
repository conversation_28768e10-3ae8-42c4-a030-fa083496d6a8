# Vue3 基础框架

## 项目简介

本项目是基于 Vue3 + TypeScript + Vite 的前端基础框架，集成了常用的开发工具和规范配置。

## 技术栈

- **框架**: Vue 3.x
- **语言**: TypeScript
- **构建工具**: Vite
- **UI框架**: UnoCSS
- **路由**: Vue Router 4.x
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **请求管理**: vue-hook-plus (useRequest)
- **代码规范**: ESLint + Prettier
- **测试框架**: Vitest

## 目录结构

```
api-manage/
├── public/                     # 静态资源目录
│   └── favicon.ico            # 网站图标
├── src/                       # 源代码目录
│   ├── api/                   # API接口层
│   │   ├── axios.d.ts         # Axios类型声明
│   │   ├── common.ts          # 通用API方法
│   │   ├── index.ts           # API导出文件
│   │   ├── request.ts         # HTTP请求封装
│   │   ├── interceptors/      # 请求/响应拦截器
│   │   │   ├── request-interceptors.ts
│   │   │   └── response-interceptors.ts
│   │   ├── models/            # 数据模型定义
│   │   │   └── user.ts
│   │   └── modules/           # API模块分类
│   │       └── user.ts
│   ├── assets/                # 静态资源
│   │   ├── base.css          # 基础样式
│   │   ├── main.css          # 主样式文件
│   │   └── logo.svg          # Logo图标
│   ├── components/            # 组件目录
│   │   ├── __tests__/        # 组件测试文件
│   │   ├── business/         # 业务组件
│   │   ├── icons/            # 图标组件
│   │   ├── layout/           # 布局组件
│   │   └── ui/               # UI基础组件
│   ├── router/                # 路由配置
│   │   └── index.ts
│   ├── stores/                # 状态管理
│   │   ├── auth.ts           # 认证相关状态
│   │   └── counter.ts        # 计数器示例
│   ├── views/                 # 页面视图
│   │   └── home-page/        # 首页相关
│   ├── App.vue               # 根组件
│   ├── main.ts               # 应用入口文件
│   └── typed-router.d.ts     # 路由类型声明
├── env.d.ts                   # 环境变量类型声明
├── eslint.config.ts          # ESLint配置
├── index.html                # HTML模板
├── package.json              # 项目依赖配置
├── pnpm-lock.yaml           # 依赖锁定文件
├── tsconfig.*.json          # TypeScript配置文件
├── uno.config.ts            # UnoCSS配置
├── vite.config.ts           # Vite配置
└── vitest.config.ts         # 测试配置
```

## 开发规范

### 1. 命名规范

#### 1.1 文件命名规范

- **组件文件**: 使用 PascalCase (大驼峰)

  ```
  UserProfile.vue
  DataTable.vue
  ```

- **页面文件**: 使用 kebab-case (短横线连接)

  ```
  user-profile.vue
  data-table.vue
  ```

- **工具类文件**: 使用 kebab-case

  ```
  date-utils.ts
  string-helper.ts
  ```

- **常量文件**: 使用 SCREAMING_SNAKE_CASE
  ```
  API_CONSTANTS.ts
  USER_ROLES.ts
  ```

#### 1.2 目录命名规范

- 统一使用 **kebab-case** (短横线连接)
- 目录名应该简洁明了，表达清楚目录用途

```
home-page/
user-management/
data-analysis/
```

#### 1.3 变量命名规范

- **变量名**: camelCase (小驼峰)
- **常量**: SCREAMING_SNAKE_CASE
- **类名**: PascalCase
- **接口名**: PascalCase，以 I 开头

```typescript
// 变量
const userName = 'admin'
const userList = []

// 常量
const API_BASE_URL = 'https://api.example.com'
const MAX_RETRY_COUNT = 3

// 接口
interface IUserInfo {
  id: number
  name: string
}

// 类
class UserService {
  // ...
}
```

### 2. 目录结构规范

#### 2.1 src/api/ - API接口层

**作用**: 统一管理所有HTTP请求相关代码

- `request.ts`: HTTP客户端配置和基础封装
- `interceptors/`: 请求和响应拦截器
- `models/`: 数据模型和类型定义
- `modules/`: 按业务模块分类的API接口
- `common.ts`: 通用API方法

**使用规范**:

```typescript
// models/user.ts - 定义数据模型
export interface IUser {
  id: number
  name: string
  email: string
}

// modules/user.ts - 用户相关API
export const userApi = {
  getUserList: () => request.get<IUser[]>('/users'),
  getUserById: (id: number) => request.get<IUser>(`/users/${id}`),
}

// 组件中使用 useRequest 进行请求管理
// UserList.vue
<script setup lang="ts">
import { useRequest } from 'vue-hook-plus'
import { userApi } from '@/api/modules/user'

// 使用 useRequest 管理请求状态
const {
  data: userList,
  loading,
  error,
  refresh,
} = useRequest(userApi.getUserList, {
  // 配置选项
  refreshDeps: [], // 依赖刷新
})
</script>
```

#### 2.2 src/components/ - 组件目录

**作用**: 存放可复用的Vue组件

- `ui/`: 基础UI组件 (Button, Input, Modal等)
- `business/`: 业务组件 (UserCard, ProductList等)
- `layout/`: 布局组件 (Header, Sidebar, Footer等)
- `icons/`: 图标组件
- `__tests__/`: 组件单元测试

**使用规范**:

- 组件名使用PascalCase
- 每个组件应该有清晰的单一职责
- 使用 `defineModel` 进行双向绑定

```vue
<!-- ui/BaseInput.vue -->
<template>
  <input v-model="model" :placeholder="placeholder" />
</template>

<script setup lang="ts">
// 推荐使用 defineModel 进行双向绑定
const model = defineModel<string>()

interface Props {
  placeholder?: string
}

defineProps<Props>()
</script>
```

#### 2.3 src/views/ - 页面视图

**作用**: 存放路由对应的页面组件

- 按功能模块分目录
- 每个模块可包含主页面和子页面
- 页面文件使用kebab-case命名

```
views/
├── home-page/
│   ├── index.vue          # 首页主文件
│   └── components/        # 首页专用组件
├── user-management/
│   ├── index.vue          # 用户管理主页
│   ├── user-list.vue      # 用户列表页
│   └── user-detail.vue    # 用户详情页
```

#### 2.4 src/stores/ - 状态管理

**作用**: 使用Pinia管理全局状态

- 按业务模块划分store
- 使用组合式API风格
- 命名使用camelCase

```typescript
// stores/user.ts
export const useUserStore = defineStore('user', () => {
  const currentUser = ref<IUser | null>(null)

  const login = async (credentials: LoginCredentials) => {
    // 登录逻辑
  }

  return {
    currentUser,
    login,
  }
})
```

#### 2.5 src/router/ - 路由配置

**作用**: 管理应用路由配置，使用 `vite-plugin-pages` 自动生成路由

- 路由基于文件系统自动生成
- 在 Vue 文件中使用 `<route>` 标签定义路由信息
- 配置路由守卫和全局配置

**使用规范**:

1. **基础路由**: 基于 `src/views/` 目录结构自动生成

   ```
   src/views/
   ├── index.vue           → /
   ├── about.vue           → /about
   ├── user/
   │   ├── index.vue       → /user
   │   ├── profile.vue     → /user/profile
   │   └── [id].vue        → /user/:id
   ```

2. **自定义路由信息**: 在 Vue 文件中使用 `<route>` 标签

   ```vue
   <!-- src/views/user/profile.vue -->
   <route lang="yaml">
   meta:
     requiresAuth: true
     title: '用户资料'
     layout: 'user'
   </route>

   <template>
     <div>用户资料页面</div>
   </template>
   ```

3. **路由参数和查询**:

   ```vue
   <!-- src/views/user/[id].vue -->
   <route lang="yaml">
   meta:
     title: '用户详情'
   </route>

   <template>
     <div>用户ID: {{ $route.params.id }}</div>
   </template>
   ```

4. **嵌套路由**: 使用文件夹结构
   ```
   src/views/
   ├── admin/
   │   ├── index.vue       → /admin
   │   ├── users.vue       → /admin/users
   │   └── settings.vue    → /admin/settings
   ```

**详情请查看 [vite-plugin-pages](https://github.com/hannoeru/vite-plugin-pages) 的文档。**

#### 2.6 src/assets/ - 静态资源

**作用**: 存放样式文件、图片、字体等静态资源

- `base.css`: 基础样式重置
- `main.css`: 主要样式文件
- `images/`: 图片资源
- `fonts/`: 字体文件

### 3. 代码规范

#### 3.1 Vue组件规范

1. **组件结构顺序**:

   ```vue
   <!-- 路由配置 (仅在页面组件中使用) -->
   <route lang="yaml">
   meta:
     title: '页面标题'
     requiresAuth: true
   </route>

   <template>
     <!-- 模板内容 -->
   </template>

   <script setup lang="ts">
   // 1. 导入
   // 2. 类型定义
   // 3. Props/Emits定义
   // 4. 响应式数据
   // 5. 计算属性
   // 6. 方法
   // 7. 生命周期钩子
   </script>

   <style scoped>
   /* 样式 */
   </style>
   ```

2. **页面组件路由配置**:

   - 在 `src/views/` 下的页面组件可使用 `<route>` 标签
   - 路由配置使用 YAML 格式
   - 常用配置项：`meta.title`、`meta.requiresAuth`、`meta.layout` 等

3. **网络请求规范**:

   - 统一使用 `vue-hook-plus` 的 `useRequest` 进行网络请求
   - 避免在组件中直接调用 API 方法
   - 利用 `useRequest` 的内置状态管理 (loading, error, data)

   ```vue
   <template>
     <div>
       <div v-if="loading">加载中...</div>
       <div v-else-if="error">请求失败: {{ error.message }}</div>
       <div v-else>
         <ul>
           <li v-for="user in userList" :key="user.id">
             {{ user.name }}
           </li>
         </ul>
       </div>
       <button @click="refresh">刷新</button>
     </div>
   </template>

   <script setup lang="ts">
   import { useRequest } from 'vue-hook-plus'
   import { userApi } from '@/api/modules/user'

   // 基础用法
   const { data: userList, loading, error, refresh } = useRequest(userApi.getUserList)

   // 带参数的请求
   const { data: userDetail, run: fetchUser } = useRequest(userApi.getUserById, {
     manual: true, // 手动触发
   })

   // 获取用户详情
   const handleGetUser = (id: number) => {
     fetchUser(id)
   }
   </script>
   ```

4. **优先使用组合式API**
5. **使用TypeScript进行类型约束**
6. **组件props必须定义类型**

#### 3.2 网络请求规范 (useRequest)

**作用**: 使用 `vue-hook-plus` 的 `useRequest` 统一管理网络请求状态

**基本原则**:

1. **统一使用 useRequest**: 所有网络请求都应该通过 `useRequest` 包装
2. **避免直接调用**: 不要在组件中直接调用 API 方法
3. **状态管理**: 充分利用 `useRequest` 的内置状态 (loading, error, data)
4. **类型安全**: 为 `useRequest` 提供正确的 TypeScript 类型

**常用配置选项**:

```typescript
// 自动请求 (组件挂载时自动执行)
const { data, loading, error, refresh } = useRequest(apiFunction, {
  refreshDeps: [dep1, dep2], // 依赖变化时自动重新请求
  cacheKey: 'unique-key', // 缓存键
  staleTime: 5000, // 缓存时间 (ms)
  debounceWait: 300, // 防抖延迟
  throttleWait: 1000, // 节流延迟
})

// 手动请求 (需要手动触发)
const { data, loading, error, run } = useRequest(apiFunction, {
  manual: true, // 手动触发
  onSuccess: (data) => {
    // 成功回调
    console.log('请求成功', data)
  },
  onError: (error) => {
    // 失败回调
    console.error('请求失败', error)
  },
})
```

**使用示例**:

1. **列表查询**:

   ```vue
   <script setup lang="ts">
   import { useRequest } from 'vue-hook-plus'
   import { userApi } from '@/api/modules/user'
   import type { IUser } from '@/api/models/user'

   // 自动加载用户列表
   const {
     data: userList,
     loading: listLoading,
     error: listError,
     refresh: refreshList,
   } = useRequest(userApi.getUserList, {
     cacheKey: 'user-list',
     staleTime: 60000, // 1分钟缓存
   })
   </script>
   ```

2. **带参数查询**:

   ```vue
   <script setup lang="ts">
   import { ref } from 'vue'
   import { useRequest } from 'vue-hook-plus'

   const userId = ref<number>()

   // 依赖 userId 变化自动请求
   const { data: userDetail, loading } = useRequest(() => userApi.getUserById(userId.value!), {
     refreshDeps: [userId],
     ready: () => !!userId.value, // 当 userId 存在时才请求
   })
   </script>
   ```

3. **表单提交**:

   ```vue
   <script setup lang="ts">
   // 手动触发的提交请求
   const { loading: submitting, run: submitForm } = useRequest(userApi.createUser, {
     manual: true,
     onSuccess: () => {
       // 提交成功后刷新列表
       refreshList()
       ElMessage.success('创建成功')
     },
     onError: (error) => {
       ElMessage.error(`创建失败: ${error.message}`)
     },
   })

   const handleSubmit = (formData: ICreateUserRequest) => {
     submitForm(formData)
   }
   </script>
   ```

4. **搜索功能**:

   ```vue
   <script setup lang="ts">
   import { ref } from 'vue'

   const keyword = ref('')

   // 带防抖的搜索
   const { data: searchResult, loading: searching } = useRequest(
     () => userApi.searchUsers(keyword.value),
     {
       refreshDeps: [keyword],
       debounceWait: 500, // 500ms 防抖
       ready: () => keyword.value.length > 0,
     },
   )
   </script>
   ```

**最佳实践**:

- 为不同的请求状态提供不同的变量名 (如: `listLoading`, `detailLoading`)
- 合理使用缓存 (`cacheKey`, `staleTime`) 提升用户体验
- 使用 `ready` 选项控制请求触发时机
- 在成功和失败回调中处理业务逻辑
- 充分利用防抖和节流功能优化性能

#### 3.3 TypeScript规范

1. **严格模式**: 开启所有严格检查
2. **类型导入**: 使用 `import type` 导入类型
3. **接口命名**: 以 `I` 开头
4. **避免使用 `any`**: 尽量使用具体类型

### 4. Git提交规范

使用约定式提交格式:

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

**类型说明**:

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式修改
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**:

```
feat(user): 添加用户登录功能
fix(api): 修复用户列表接口返回数据格式问题
docs(readme): 更新项目说明文档
```

## 开发流程

1. **创建分支**: 从主分支创建功能分支
2. **开发**: 按照规范进行开发
3. **测试**: 编写并运行单元测试
4. **代码检查**: 运行 ESLint 和类型检查
5. **提交代码**: 使用规范的commit message
6. **创建PR**: 提交Pull Request进行代码审查

## 常用命令

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 运行测试
pnpm test

# 代码检查
pnpm lint

# 类型检查
pnpm type-check
```

## 注意事项

1. **代码提交前必须通过ESLint检查**
2. **新增组件必须编写单元测试**
3. **API接口必须定义TypeScript类型**
4. **统一使用useRequest进行网络请求，禁止直接调用API方法**
5. **使用defineModel进行组件双向绑定**
6. **遵循单一职责原则，保持组件简洁**
7. **及时更新文档和注释**
